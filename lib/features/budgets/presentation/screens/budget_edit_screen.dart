import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/budget.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_deletion_dialog.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_form.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/features/budgets/services/budget_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/common/loading_indicator.dart';
import 'package:budapp/widgets/navigation/global_fab_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Budget edit screen
class BudgetEditScreen extends ConsumerStatefulWidget {
  const BudgetEditScreen({super.key, required this.budgetId});
  final String budgetId;

  @override
  ConsumerState<BudgetEditScreen> createState() => _BudgetEditScreenState();
}

class _BudgetEditScreenState extends ConsumerState<BudgetEditScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final budgetAsync = ref.watch(budgetProvider(widget.budgetId));

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.editBudget),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(DesignTokens.spacing16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
      body: budgetAsync.when(
        loading: () => const LoadingIndicator(),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: DesignTokens.spacing16),
              Text(
                l10n.errorLoadingBudget,
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: DesignTokens.spacing16),
              FilledButton(
                onPressed: () =>
                    ref.invalidate(budgetProvider(widget.budgetId)),
                child: Text(l10n.retry),
              ),
            ],
          ),
        ),
        data: (budget) => Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(DesignTokens.spacing16),
                  child: BudgetForm(
                    initialBudget: budget,
                    onSubmit: _updateBudget,
                    isLoading: _isLoading,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ).withFormFabs(
      mode: FabMode.formEdit,
      onSave: _isLoading ? null : _submitForm,
      onCancel: () => context.pop(),
      onDelete: _showDeleteConfirmation,
      isLoading: _isLoading,
    );
  }

  /// Submit the form
  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      _formKey.currentState?.save();
    }
  }

  /// Update budget with enhanced error handling
  Future<void> _updateBudget(Budget budget) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Call the controller to update the budget
      await ref.read(budgetsControllerProvider.notifier).updateBudget(budget);

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Check the controller state to see if the operation succeeded or failed
      final controllerState = ref.read(budgetsControllerProvider);

      if (controllerState.hasError) {
        // Handle error case
        final error = controllerState.error;
        var errorMessage = AppLocalizations.of(context)!.errorUpdatingBudget;

        if (error is BudgetException) {
          errorMessage = BudgetErrorService.getUserFriendlyMessage(error);
        } else if (error is Exception) {
          final budgetError = BudgetErrorService.handleError(error);
          errorMessage = BudgetErrorService.getUserFriendlyMessage(budgetError);
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white, size: 20),
                const SizedBox(width: DesignTokens.spacing8),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () => _updateBudget(budget),
            ),
          ),
        );
      } else {
        // Handle success case
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: DesignTokens.spacing8),
                Expanded(
                  child: Text(AppLocalizations.of(context)!.budgetUpdated),
                ),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );

        // Navigate back to budgets list
        context.pop();
      }

      setState(() {
        _isLoading = false;
      });
    } on Exception {
      // Handle any unexpected errors that might occur
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show delete confirmation dialog
  Future<void> _showDeleteConfirmation() async {
    final budgetAsync = ref.read(budgetProvider(widget.budgetId));
    final budget = budgetAsync.value;
    if (budget == null) return;
    final l10n = AppLocalizations.of(context)!;

    // Use the enhanced deletion dialog
    await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => BudgetDeletionDialog(
        budget: budget,
        onDeleted: () {
          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: DesignTokens.spacing8),
                    Expanded(child: Text(l10n.budgetDeleted)),
                  ],
                ),
                backgroundColor: Theme.of(context).colorScheme.primary,
                behavior: SnackBarBehavior.floating,
                duration: const Duration(seconds: 3),
              ),
            );

            // Navigate back to budgets list
            context.pop();
          }
        },
      ),
    );
  }
}
